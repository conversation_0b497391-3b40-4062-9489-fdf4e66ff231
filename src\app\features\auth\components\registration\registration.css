/* Registration page specific styles */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

/* Enhanced floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-25px) rotate(-1deg);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

/* Animated gradient background */
.animated-gradient {
  background: linear-gradient(-45deg,
    #3b82f6 0%,
    #7c3aed 15%,
    #1e1b4b 30%,
    #764ba2 45%,
    #000000 60%,
    #581c87 75%,
    #1e40af 90%,
    #000000 100%);
  background-size: 400% 400%;
  animation: gradient-shift 25s ease infinite;
}

/* Form input focus effects */
.form-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(168, 85, 247, 0.2);
}

/* Button hover effects */
.register-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(147, 51, 234, 0.4);
}

.confirm-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.confirm-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(34, 197, 94, 0.4);
}

/* Custom backdrop blur for better browser support */
.backdrop-blur-custom {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .animate-float {
    animation-duration: 6s;
  }

  .backdrop-blur-custom {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Hide some floating elements on mobile */
  .floating-element-mobile-hidden {
    display: none;
  }
}

@media (max-width: 480px) {
  /* Reduce floating element sizes on very small screens */
  .floating-element {
    width: 2rem;
    height: 2rem;
  }
}

/* Network animation */
@keyframes network-pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}

.network-animation {
  animation: network-pulse 4s ease-in-out infinite;
}

/* Logo styling */
.logo-text {
  font-family: 'Arial', sans-serif;
  font-weight: 900;
  letter-spacing: 0.1em;
  line-height: 0.9;
}

/* Form container styling */
.form-container {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Input field styling */
.input-field {
  transition: all 0.3s ease;
}

.input-field:focus {
  background: rgba(0, 0, 0, 0.5) !important;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.input-field::placeholder {
  color: rgba(209, 213, 219, 0.7);
}

/* Google button styling */
.google-button {
  transition: all 0.3s ease;
}

.google-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Code field animation */
.code-field-enter {
  animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
