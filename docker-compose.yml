version: '3.8'

services:
  # Default environment service
  web-default:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - ENVIRONMENT=default
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
      - SERVER_IP=${SERVER_IP:-127.0.0.1}
    ports:
      - "${HTTP_PORT:-80}:80"
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - default

  # Acidification environment service
  web-acidification:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - ENVIRONMENT=acidification
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
      - SERVER_IP=${SERVER_IP:-127.0.0.1}
    ports:
      - "${HTTP_PORT:-80}:80"
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - acidification

networks:
  app-network:
    driver: bridge