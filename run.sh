#!/bin/bash

# Script to run the application in different environments
set -e

ENVIRONMENT=${1:-default}
ACTION=${2:-up}

echo "Running toy-for-toi in $ENVIRONMENT environment..."

# Load environment variables if .env file exists
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | xargs)
fi

case $ENVIRONMENT in
    "default")
        echo "Starting default environment..."
        docker-compose --profile default $ACTION -d
        ;;
    "acidification")
        echo "Starting acidification environment..."
        docker-compose --profile acidification $ACTION -d
        ;;
    *)
        echo "Usage: $0 [default|acidification] [up|down|build|logs]"
        echo ""
        echo "Examples:"
        echo "  $0 default up          # Start default environment"
        echo "  $0 acidification up    # Start acidification environment"
        echo "  $0 default down        # Stop default environment"
        echo "  $0 acidification logs  # View acidification logs"
        echo ""
        echo "Make sure to copy .env.example to .env and configure your settings first!"
        exit 1
        ;;
esac

if [ "$ACTION" = "up" ]; then
    echo ""
    echo "Application is starting..."
    echo "You can view logs with: docker-compose --profile $ENVIRONMENT logs -f"
    echo "To stop: docker-compose --profile $ENVIRONMENT down"
fi
