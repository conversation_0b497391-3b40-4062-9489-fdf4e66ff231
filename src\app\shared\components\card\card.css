/* Game Card Specific Styles */
.game-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.game-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 2px;
  background: linear-gradient(135deg, #a855f7, #ec4899, #3b82f6);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.game-card:hover::before {
  opacity: 0.6;
}

/* Video iframe styling */
.game-video {
  border-radius: 0.75rem;
  overflow: hidden;
}

/* Feature badges */
.feature-badge {
  background: rgba(162, 132, 94, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.feature-badge:hover {
  background: rgba(162, 132, 94, 1);
  transform: scale(1.05);
}

/* Price styling */
.price-text {
  background: linear-gradient(135deg, #ffb94a, #ff8c00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button styling */
.action-button {
  background: linear-gradient(135deg, #ff9500, #ff7700);
  transition: all 0.3s ease;
}

.action-button:hover {
  background: linear-gradient(135deg, #ff7700, #ff5500);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 149, 0, 0.4);
}