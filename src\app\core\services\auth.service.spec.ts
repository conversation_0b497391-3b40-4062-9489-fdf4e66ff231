import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Router } from '@angular/router';
import { AuthService, LoginRequest, LoginResponse } from './auth.service';
import { LocalStorageService } from './local-storage.service';
import { environment } from '../../environments/environment';

describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;
  let localStorageService: jasmine.SpyObj<LocalStorageService>;
  let router: jasmine.SpyObj<Router>;

  const mockLoginResponse: LoginResponse = {
    refresh: 'mock-refresh-token',
    access: 'mock-access-token',
    user_id: 1,
    email: '<EMAIL>',
    is_staff: false
  };

  beforeEach(() => {
    const localStorageSpy = jasmine.createSpyObj('LocalStorageService', [
      'setTokens', 'getAccessToken', 'getRefreshToken', 'getUserData', 
      'updateAccessToken', 'clearTokens', 'hasValidToken'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AuthService,
        { provide: LocalStorageService, useValue: localStorageSpy },
        { provide: Router, useValue: routerSpy }
      ]
    });

    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
    localStorageService = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should login successfully', () => {
    const loginRequest: LoginRequest = {
      email: '<EMAIL>',
      password: 'password123'
    };

    service.login(loginRequest).subscribe(response => {
      expect(response).toEqual(mockLoginResponse);
      expect(localStorageService.setTokens).toHaveBeenCalledWith(mockLoginResponse);
    });

    const req = httpMock.expectOne(`${environment.apiUrl}/api/token/`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(loginRequest);
    req.flush(mockLoginResponse);
  });

  it('should handle login error', () => {
    const loginRequest: LoginRequest = {
      email: '<EMAIL>',
      password: 'wrongpassword'
    };

    service.login(loginRequest).subscribe({
      next: () => fail('should have failed'),
      error: (error) => {
        expect(error.message).toBe('Invalid email or password');
      }
    });

    const req = httpMock.expectOne(`${environment.apiUrl}/api/token/`);
    req.flush({ error: 'Invalid credentials' }, { status: 401, statusText: 'Unauthorized' });
  });

  it('should logout successfully', () => {
    service.logout();
    
    expect(localStorageService.clearTokens).toHaveBeenCalled();
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });

  it('should check authentication status', () => {
    localStorageService.hasValidToken.and.returnValue(true);
    
    expect(service.isAuthenticated()).toBe(true);
    expect(localStorageService.hasValidToken).toHaveBeenCalled();
  });
});
