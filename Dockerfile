# Build stage
FROM node:20-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Install envsubst for environment variable substitution
RUN apk add --no-cache gettext

# Copy built application
COPY --from=build /app/dist/toy-for-toi/browser /usr/share/nginx/html

# Copy nginx configurations
COPY nginx.conf /tmp/nginx.conf
COPY nginx-acidification.conf /tmp/nginx-acidification.conf

# Copy setup script
COPY scripts/setup-nginx.sh /usr/local/bin/setup-nginx.sh
RUN chmod +x /usr/local/bin/setup-nginx.sh

# Create entrypoint script
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo 'set -e' >> /docker-entrypoint.sh && \
    echo '/usr/local/bin/setup-nginx.sh' >> /docker-entrypoint.sh && \
    echo 'exec "$@"' >> /docker-entrypoint.sh && \
    chmod +x /docker-entrypoint.sh

EXPOSE 80
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]