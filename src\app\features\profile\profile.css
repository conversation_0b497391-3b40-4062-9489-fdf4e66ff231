/* Profile-specific styles */
.animated-gradient {
  background: linear-gradient(45deg, #3b82f6, #7c3aed, #1e40af, #3b82f6);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.network-animation {
  animation: networkPulse 4s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}
