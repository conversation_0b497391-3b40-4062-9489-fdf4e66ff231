<!-- Main Container with Unified Gradient Background -->
<div class="min-h-screen relative overflow-hidden">
  <!-- Unified animated gradient background for entire page -->
  <div
    class="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900/80 to-black"
  ></div>
  <div class="fixed inset-0 animated-gradient opacity-15"></div>
  <div
    class="fixed inset-0 bg-gradient-to-b from-blue-900/10 via-purple-950/30 to-black/50"
  ></div>

  <app-header />

  <!-- Floating decorative elements -->
  <div class="fixed inset-0 pointer-events-none z-5">
    <!-- Bottom left floating element -->
    <div
      class="absolute bottom-20 left-20 w-4 h-4 bg-white/20 rounded-full animate-float"
    ></div>
    <div
      class="absolute bottom-32 left-32 w-2 h-2 bg-blue-400/30 rounded-full animate-float"
      style="animation-delay: 2s"
    ></div>

    <!-- Top left floating elements -->
    <div
      class="absolute top-40 left-16 w-3 h-3 bg-purple-400/25 rounded-full animate-float"
      style="animation-delay: 1s"
    ></div>
    <div
      class="absolute top-60 left-40 w-2 h-2 bg-white/15 rounded-full animate-float"
      style="animation-delay: 3s"
    ></div>

    <!-- Right side floating elements -->
    <div
      class="absolute top-1/3 right-20 w-3 h-3 bg-blue-300/20 rounded-full animate-float"
      style="animation-delay: 4s"
    ></div>
    <div
      class="absolute bottom-1/3 right-32 w-2 h-2 bg-purple-300/25 rounded-full animate-float"
      style="animation-delay: 1.5s"
    ></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 min-h-screen flex items-center justify-center px-4">
    <div class="w-full max-w-2xl">
      <!-- Profile Title -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-black text-white">Профиль пользователя</h1>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="text-center">
        <div class="inline-flex items-center px-6 py-3 bg-black/30 backdrop-blur-sm border border-purple-400/30 rounded-lg">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-white">Загрузка профиля...</span>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="errorMessage && !isLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
        <p class="text-red-300">{{ errorMessage }}</p>
        <button 
          (click)="refreshProfile()"
          class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Попробовать снова
        </button>
      </div>

      <!-- Profile Content -->
      <div *ngIf="userProfile && !isLoading" class="space-y-6">
        <!-- Profile Card -->
        <div class="bg-black/30 backdrop-blur-sm border border-purple-400/30 rounded-lg p-6">
          <div class="space-y-4">
            <!-- User ID -->
            <div class="flex justify-between items-center">
              <span class="text-gray-300">ID пользователя:</span>
              <span class="text-white font-medium">{{ userProfile?.id }}</span>
            </div>

            <!-- Email -->
            <div class="flex justify-between items-center">
              <span class="text-gray-300">Email:</span>
              <span class="text-white font-medium">{{ userProfile?.email }}</span>
            </div>

            <!-- Staff Status -->
            <div class="flex justify-between items-center">
              <span class="text-gray-300">Статус:</span>
              <span
                [class]="userProfile?.is_staff ? 'text-green-400' : 'text-blue-400'"
                class="font-medium"
              >
                {{ userProfile?.is_staff ? 'Администратор' : 'Пользователь' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Refresh Profile -->
          <button
            (click)="refreshProfile()"
            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-[1.02]"
          >
            Обновить профиль
          </button>

          <!-- Verify Token -->
          <button
            (click)="verifyToken()"
            class="px-6 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-teal-700 transition-all transform hover:scale-[1.02]"
          >
            Проверить токен
          </button>

          <!-- Logout -->
          <button
            (click)="onLogout()"
            class="px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 text-white font-medium rounded-lg shadow-lg hover:from-red-700 hover:to-pink-700 transition-all transform hover:scale-[1.02]"
          >
            Выйти
          </button>
        </div>

        <!-- Navigation -->
        <div class="text-center">
          <a 
            routerLink="/"
            class="text-blue-400 hover:text-blue-300 transition-colors font-medium"
          >
            ← Вернуться на главную
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom decorative network pattern -->
  <div class="fixed bottom-0 left-0 right-0 h-64 pointer-events-none z-5">
    <svg
      class="network-animation w-full h-full opacity-20"
      viewBox="0 0 1200 300"
      preserveAspectRatio="none"
    >
      <defs>
        <linearGradient
          id="networkGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 0.6" />
          <stop offset="50%" style="stop-color: #7c3aed; stop-opacity: 0.4" />
          <stop offset="100%" style="stop-color: #1e40af; stop-opacity: 0.6" />
        </linearGradient>
      </defs>
      <!-- Network lines -->
      <g stroke="url(#networkGradient)" stroke-width="1" fill="none">
        <path
          d="M0,200 L200,150 L400,180 L600,120 L800,160 L1000,140 L1200,170"
        />
        <path
          d="M0,220 L150,180 L350,200 L550,160 L750,190 L950,170 L1200,200"
        />
        <path
          d="M0,240 L100,200 L300,220 L500,180 L700,210 L900,190 L1200,220"
        />
        <!-- Connecting lines -->
        <path d="M200,150 L150,180" />
        <path d="M400,180 L350,200" />
        <path d="M600,120 L550,160" />
        <path d="M800,160 L750,190" />
        <path d="M1000,140 L950,170" />
      </g>
      <!-- Network nodes -->
      <g fill="url(#networkGradient)">
        <circle cx="200" cy="150" r="3" />
        <circle cx="400" cy="180" r="3" />
        <circle cx="600" cy="120" r="3" />
        <circle cx="800" cy="160" r="3" />
        <circle cx="1000" cy="140" r="3" />
        <circle cx="150" cy="180" r="2" />
        <circle cx="350" cy="200" r="2" />
        <circle cx="550" cy="160" r="2" />
        <circle cx="750" cy="190" r="2" />
        <circle cx="950" cy="170" r="2" />
      </g>
    </svg>
  </div>
</div>
