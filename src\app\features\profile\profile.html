<!-- Main Container with Unified Gradient Background -->
<div class="min-h-screen relative overflow-hidden">
  <!-- Unified animated gradient background for entire page -->
  <div
    class="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900/80 to-black"
  ></div>
  <div class="fixed inset-0 animated-gradient opacity-15"></div>
  <div
    class="fixed inset-0 bg-gradient-to-b from-blue-900/10 via-purple-950/30 to-black/50"
  ></div>

  <!-- Floating decorative elements -->
  <div class="fixed inset-0 pointer-events-none z-5">
    <!-- Bottom left floating element -->
    <div
      class="absolute bottom-20 left-80 w-4 h-4 bg-white/20 rounded-full animate-float"
    ></div>

    <!-- Top left floating elements -->
    <div
      class="absolute top-40 left-80 w-3 h-3 bg-purple-400/25 rounded-full animate-float"
      style="animation-delay: 1s"
    ></div>
    <div
      class="absolute top-60 left-96 w-2 h-2 bg-white/15 rounded-full animate-float"
      style="animation-delay: 3s"
    ></div>

    <!-- Right side floating elements -->
    <div
      class="absolute top-1/3 right-20 w-3 h-3 bg-blue-300/20 rounded-full animate-float"
      style="animation-delay: 4s"
    ></div>
    <div
      class="absolute bottom-1/3 right-32 w-2 h-2 bg-purple-300/25 rounded-full animate-float"
      style="animation-delay: 1.5s"
    ></div>
  </div>

  <!-- Main Layout Container -->
  <div class="relative z-10 min-h-screen flex profile-layout">
    <!-- Left Sidebar Panel -->
    <div class="w-80 bg-gradient-to-b from-purple-600 via-purple-700 to-purple-800 flex flex-col profile-sidebar">
      <!-- Logo Section -->
      <div class="p-8 text-center border-b border-purple-500/30">
        <h1 class="text-2xl font-black text-white tracking-wider">
          TOY<br>
          FOR<br>
          TOI
        </h1>
      </div>

      <!-- Navigation Menu -->
      <div class="flex-1 p-6">
        <!-- Account Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Учетная запись</h2>
          <div class="space-y-2">
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Пароль и безопасность
            </a>
          </div>
        </div>

        <!-- Games Section -->
        <div class="mb-8">
          <h2 class="text-white font-semibold text-lg mb-4">Мои игры</h2>
          <div class="space-y-2">
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Библиотека игр (3)
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Мои ключи (2)
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              Мой тариф
            </a>
            <a href="#" class="block text-gray-200 hover:text-white transition-colors py-2 px-3 rounded hover:bg-purple-600/50">
              История покупок
            </a>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="p-6 border-t border-purple-500/30">
        <button
          (click)="onLogout()"
          class="w-full flex items-center justify-center px-4 py-3 text-white hover:text-gray-200 transition-colors"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Выйти из профиля
        </button>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 p-8 overflow-y-auto profile-content">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex items-center justify-center h-full">
        <div class="inline-flex items-center px-6 py-3 bg-black/30 backdrop-blur-sm border border-purple-400/30 rounded-lg">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-white">Загрузка профиля...</span>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="errorMessage && !isLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
        <p class="text-red-300">{{ errorMessage }}</p>
        <button
          (click)="refreshProfile()"
          class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          Попробовать снова
        </button>
      </div>

      <!-- Profile Content -->
      <div *ngIf="userProfile && !isLoading" class="max-w-4xl">
        <!-- Header with User Icon and Title -->
        <div class="flex items-center mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-6">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-4xl font-bold text-white mb-2">Параметры</h1>
            <p class="text-gray-300">Управляйте данными своей учетной записи</p>
          </div>
        </div>

        <!-- Account Information Section -->
        <div class="bg-black/20 backdrop-blur-sm border border-gray-600/30 rounded-lg p-8 mb-8 profile-section">
          <h2 class="text-2xl font-semibold text-white mb-6">Информация об учетной записи</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 profile-grid">
            <!-- Email Field -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">
                Адрес электронной почты
              </label>
              <div class="relative">
                <input
                  type="email"
                  [value]="userProfile?.email"
                  readonly
                  class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input"
                />
                <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Password Field -->
            <div>
              <label class="block text-gray-300 text-sm font-medium mb-2">
                Пароль
              </label>
              <div class="relative">
                <input
                  type="password"
                  value="••••••••••"
                  readonly
                  class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input"
                />
                <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Phone Field -->
            <div class="md:col-span-2">
              <label class="block text-gray-300 text-sm font-medium mb-2">
                Моб. телефон
              </label>
              <div class="relative">
                <input
                  type="tel"
                  placeholder="+7 (___) ___-__-__"
                  class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent profile-input"
                />
                <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Save Changes Button -->
          <div class="mt-8">
            <button class="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg shadow-lg hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-[1.02] profile-button">
              Сохранить изменения
            </button>
          </div>
        </div>

        <!-- Additional Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <!-- Refresh Profile -->
          <button
            (click)="refreshProfile()"
            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-[1.02] profile-button"
          >
            Обновить профиль
          </button>

          <!-- Verify Token -->
          <button
            (click)="verifyToken()"
            class="px-6 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-teal-700 transition-all transform hover:scale-[1.02] profile-button"
          >
            Проверить токен
          </button>

          <!-- Back to Main -->
          <a
            routerLink="/"
            class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white font-medium rounded-lg shadow-lg hover:from-gray-700 hover:to-gray-800 transition-all transform hover:scale-[1.02] text-center profile-button"
          >
            На главную
          </a>
        </div>

        <!-- User Status Info -->
        <div class="bg-black/20 backdrop-blur-sm border border-gray-600/30 rounded-lg p-6 profile-section">
          <h3 class="text-lg font-semibold text-white mb-4">Статус аккаунта</h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-gray-300">ID пользователя:</span>
              <span class="text-white font-medium">{{ userProfile?.id }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-300">Статус:</span>
              <span
                [class]="userProfile?.is_staff ? 'text-green-400' : 'text-blue-400'"
                class="font-medium"
              >
                {{ userProfile?.is_staff ? 'Администратор' : 'Пользователь' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom decorative network pattern -->
  <div class="fixed bottom-0 left-0 right-0 h-64 pointer-events-none z-5">
    <svg
      class="network-animation w-full h-full opacity-20"
      viewBox="0 0 1200 300"
      preserveAspectRatio="none"
    >
      <defs>
        <linearGradient
          id="networkGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 0.6" />
          <stop offset="50%" style="stop-color: #7c3aed; stop-opacity: 0.4" />
          <stop offset="100%" style="stop-color: #1e40af; stop-opacity: 0.6" />
        </linearGradient>
      </defs>
      <!-- Network lines -->
      <g stroke="url(#networkGradient)" stroke-width="1" fill="none">
        <path
          d="M0,200 L200,150 L400,180 L600,120 L800,160 L1000,140 L1200,170"
        />
        <path
          d="M0,220 L150,180 L350,200 L550,160 L750,190 L950,170 L1200,200"
        />
        <path
          d="M0,240 L100,200 L300,220 L500,180 L700,210 L900,190 L1200,220"
        />
        <!-- Connecting lines -->
        <path d="M200,150 L150,180" />
        <path d="M400,180 L350,200" />
        <path d="M600,120 L550,160" />
        <path d="M800,160 L750,190" />
        <path d="M1000,140 L950,170" />
      </g>
      <!-- Network nodes -->
      <g fill="url(#networkGradient)">
        <circle cx="200" cy="150" r="3" />
        <circle cx="400" cy="180" r="3" />
        <circle cx="600" cy="120" r="3" />
        <circle cx="800" cy="160" r="3" />
        <circle cx="1000" cy="140" r="3" />
        <circle cx="150" cy="180" r="2" />
        <circle cx="350" cy="200" r="2" />
        <circle cx="550" cy="160" r="2" />
        <circle cx="750" cy="190" r="2" />
        <circle cx="950" cy="170" r="2" />
      </g>
    </svg>
  </div>
</div>
