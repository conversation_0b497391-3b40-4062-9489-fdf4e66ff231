/* Header specific styles */
.header-backdrop {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Smooth transitions for mobile menu */
.mobile-menu-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Logo hover effect */
.logo-hover:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Navigation link hover effects */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #a855f7, #ec4899);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}