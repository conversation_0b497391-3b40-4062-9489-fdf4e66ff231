#!/bin/bash

# Setup nginx configuration based on environment
set -e

# Default values
DOMAIN_NAME=${DOMAIN_NAME:-localhost}
SERVER_IP=${SERVER_IP:-127.0.0.1}
ENVIRONMENT=${ENVIRONMENT:-default}

echo "Setting up nginx configuration for environment: $ENVIRONMENT"
echo "Domain: $DOMAIN_NAME"
echo "Server IP: $SERVER_IP"

# Create nginx config directory if it doesn't exist
mkdir -p /etc/nginx/conf.d

# Choose the appropriate nginx configuration
if [ "$ENVIRONMENT" = "acidification" ]; then
    echo "Using acidification nginx configuration..."
    # Replace environment variables in the acidification config
    envsubst '${DOMAIN_NAME} ${SERVER_IP}' < /tmp/nginx-acidification.conf > /etc/nginx/conf.d/default.conf
else
    echo "Using default nginx configuration..."
    cp /tmp/nginx.conf /etc/nginx/conf.d/default.conf
fi

echo "Nginx configuration setup complete!"
