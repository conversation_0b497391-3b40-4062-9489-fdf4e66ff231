import { Injectable } from '@angular/core';

export interface TokenData {
  access: string;
  refresh: string;
  user_id: number;
  email: string;
  is_staff: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LocalStorageService {
  private readonly ACCESS_TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_DATA_KEY = 'user_data';

  constructor() { }

  // Token management
  setTokens(tokenData: TokenData): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, tokenData.access);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, tokenData.refresh);
    
    const userData = {
      user_id: tokenData.user_id,
      email: tokenData.email,
      is_staff: tokenData.is_staff
    };
    localStorage.setItem(this.USER_DATA_KEY, JSON.stringify(userData));
  }

  getAccessToken(): string | null {
    return localStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  getUserData(): any | null {
    const userData = localStorage.getItem(this.USER_DATA_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  updateAccessToken(token: string): void {
    localStorage.setItem(this.ACCESS_TOKEN_KEY, token);
  }

  clearTokens(): void {
    localStorage.removeItem(this.ACCESS_TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_DATA_KEY);
  }

  isTokenExpired(token: string): boolean {
    if (!token) return true;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  hasValidToken(): boolean {
    const accessToken = this.getAccessToken();
    return accessToken !== null && !this.isTokenExpired(accessToken);
  }
}
