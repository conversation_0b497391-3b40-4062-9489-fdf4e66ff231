# Nginx Configuration Setup

This project now supports two separate nginx configurations for different environments:

## Configurations

### 1. Default Configuration (`nginx.conf`)
- Basic nginx setup for development/local environments
- Server name: `localhost`
- Standard security headers and caching rules

### 2. Acidification Configuration (`nginx-acidification.conf`)
- Production-ready configuration with enhanced security
- Configurable domain name and server IP
- Additional security headers (HSTS, CSP, etc.)
- Rate limiting
- Real IP configuration for proxy setups
- Enhanced logging

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Edit `.env` with your settings:
```bash
# Domain name for the application
DOMAIN_NAME=your-domain.com

# Server IP address (for real IP configuration)
SERVER_IP=your.server.ip.address

# Environment type (default or acidification)
ENVIRONMENT=acidification

# Port mapping
HTTP_PORT=80
```

## Usage

### Quick Start

1. **Default Environment** (Development):
```bash
./run.sh default up
```

2. **Acidification Environment** (Production):
```bash
./run.sh acidification up
```

### Manual Docker Compose

1. **Default Environment**:
```bash
docker-compose --profile default up -d
```

2. **Acidification Environment**:
```bash
docker-compose --profile acidification up -d
```

### Other Commands

```bash
# View logs
./run.sh default logs
./run.sh acidification logs

# Stop services
./run.sh default down
./run.sh acidification down

# Rebuild
./run.sh default build
./run.sh acidification build
```

## Configuration Details

### Default Environment
- Uses `nginx.conf`
- Basic security headers
- Simple caching rules
- No environment variable substitution

### Acidification Environment
- Uses `nginx-acidification.conf`
- Environment variable substitution for `${DOMAIN_NAME}` and `${SERVER_IP}`
- Enhanced security headers:
  - Strict-Transport-Security
  - Content-Security-Policy
  - Referrer-Policy
- Rate limiting (10 requests/second with burst of 20)
- Real IP configuration for proxy setups
- Enhanced logging

## File Structure

```
├── nginx.conf                 # Default nginx configuration
├── nginx-acidification.conf   # Acidification nginx configuration
├── docker-compose.yml         # Docker compose with profiles
├── Dockerfile                 # Multi-stage build with nginx setup
├── scripts/
│   └── setup-nginx.sh        # Script to configure nginx based on environment
├── .env.example              # Environment variables template
├── .env                      # Your environment configuration (create this)
└── run.sh                    # Convenience script to run environments
```

## Security Features (Acidification)

- **HSTS**: Forces HTTPS connections
- **CSP**: Content Security Policy to prevent XSS
- **Rate Limiting**: Prevents abuse
- **Real IP**: Proper client IP detection behind proxies
- **Security Headers**: X-Frame-Options, X-XSS-Protection, etc.

## Troubleshooting

1. **Environment variables not working**: Make sure you have a `.env` file with the correct values
2. **Permission denied on run.sh**: Run `chmod +x run.sh`
3. **Port conflicts**: Change `HTTP_PORT` in your `.env` file
4. **Domain not resolving**: Update your DNS or `/etc/hosts` file to point to your server IP
