import { Component, OnInit } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.html',
  styleUrl: './profile.css'
})
export class Profile implements OnInit {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        alert('Token is valid!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        alert('Token verification failed: ' + error.message);
      }
    });
  }
}
