import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { Login } from './components/login/login';
import { Registration } from './components/registration/registration';
import { SharedModule } from "../../shared/shared.module";

@NgModule({
    declarations: [
    Login,
    Registration
  ],
    imports: [
    CommonModule,
    ReactiveFormsModule,
    SharedModule
]
})
export class AuthModule { }
